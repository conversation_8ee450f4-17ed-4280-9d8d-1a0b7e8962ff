import React, { useState, useEffect, useRef } from 'react';
import { useLinkedIn } from './LinkedInContext';
import {
  Box,
  Card,
  CardContent,
  Typography,
  TextField,
  Button,
  Switch,
  FormControlLabel,
  Alert,
  LinearProgress,
  Chip,
  Grid,
  List,
  ListItem,
  ListItemText,
  Divider,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Snackbar,
  Container,
  Paper,
  IconButton,
  Menu,
  MenuItem,
  Tooltip,
  Select,
  FormControl,
  InputLabel,
  Checkbox,
  Link,
  Pagination,
  Stack,
  CircularProgress
} from '@mui/material';
import {
  PlayArrow as PlayIcon,
  Stop as StopIcon,
  Search as SearchIcon,
  CheckCircle as CheckIcon,
  Error as ErrorIcon,
  Settings as SettingsIcon,
  Info as InfoIcon,
  Refresh as RefreshIcon,
  FilterList as FilterIcon,
  Clear as ClearIcon,
  ExpandMore as ExpandMoreIcon,
  ExpandLess as ExpandLessIcon,
  Terminal as TerminalIcon,
  Logout as LogoutIcon
} from '@mui/icons-material';

const LinkedInAutomation = () => {
  // 使用全局LinkedIn状态
  const {
    searchResults,
    selectedJobs,
    searchForm,
    currentPage,
    itemsPerPage,
    automationStatus,
    batchSettings,
    totalPages,
    startIndex,
    endIndex,
    currentPageJobs,
    isAllSelected,
    isIndeterminate,
    updateSearchResults,
    updateSelectedJobs,
    updateSearchForm,
    updateCurrentPage,
    updateItemsPerPage,
    setAutomationStatus,
    updateBatchSettings
  } = useLinkedIn();

  // 本地状态（不需要跨页面保持的状态）
  const [loginForm, setLoginForm] = useState({
    email: '',
    password: '',
    headless: false,
    automation_type: 'selenium' // 默认selenium，根据memories更稳定
  });

  const [batchApplyForm, setBatchApplyForm] = useState({
    max_applications: '',
    keywords: [],
    location: ''
  });

  const [loading, setLoading] = useState(false);
  const [searchLoading, setSearchLoading] = useState(false); // 专门用于搜索状态
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [showLoginDialog, setShowLoginDialog] = useState(false);
  
  // 日志相关状态
  const [logs, setLogs] = useState([]);
  const [logLevel, setLogLevel] = useState('INFO'); // 默认显示INFO级别及以上
  const [logLines, setLogLines] = useState(50); // 默认显示最近50行
  const [logTimestamp, setLogTimestamp] = useState(0); // 用于追踪最新日志
  const [expandLogs, setExpandLogs] = useState(false); // 控制日志面板展开/收起
  const [anchorEl, setAnchorEl] = useState(null); // 日志级别菜单锚点
  const [userScrolled, setUserScrolled] = useState(false); // 跟踪用户是否手动滚动

  // 日志滚动相关引用
  const logEndRef = useRef(null);
  const logContainerRef = useRef(null);


  // API基础URL
  const API_BASE = 'http://localhost:8003/api/linkedin';

  // 获取自动化状态
  const fetchStatus = async () => {
    try {
      const response = await fetch(`${API_BASE}/status`);
      if (response.ok) {
        const data = await response.json();
        setAutomationStatus(data);

        // 检查后端状态，如果后端重启了（找到职位为0且没有登录），清空前端缓存的搜索结果
        if (data.progress && data.progress.total_found === 0 && !data.is_logged_in && searchResults.length > 0) {
          console.log('检测到后端重启，清空前端缓存的搜索结果');
          updateSearchResults([]);
        }

        // 额外检查：如果后端没有progress数据但前端有搜索结果，也清空缓存
        if (!data.progress && searchResults.length > 0) {
          console.log('后端无progress数据，清空前端缓存');
          updateSearchResults([]);
        }
      }
    } catch (err) {
      console.error('获取状态失败:', err);
    }
  };
  
  // 获取日志
  const fetchLogs = async (force = false) => {
    try {
      // 构建查询参数
      const params = new URLSearchParams({
        lines: logLines,
        level: logLevel
      });
      
      // 如果有时间戳且不是强制刷新，则添加时间戳参数
      if (logTimestamp && !force) {
        params.append('timestamp', logTimestamp);
      }
      
      const response = await fetch(`${API_BASE}/logs?${params.toString()}`);
      if (response.ok) {
        const data = await response.json();
        
        // 只有当有新日志或强制刷新时才更新状态
        if (data.logs.length > 0 || force) {
          setLogs(data.logs);
          setLogTimestamp(data.timestamp);
          
          // 只有当用户没有手动滚动时才自动滚动到底部
          if (logContainerRef.current && !userScrolled) {
            // 使用setTimeout确保DOM更新后再滚动
            setTimeout(() => {
              if (logContainerRef.current) {
                // 直接滚动容器到底部，避免影响页面滚动
                logContainerRef.current.scrollTop = logContainerRef.current.scrollHeight;
              }
            }, 100);
          }
        }
      }
    } catch (err) {
      console.error('获取日志失败:', err);
    }
  };

  // 定期更新状态和日志
  useEffect(() => {
    fetchStatus();
    fetchLogs();

    const statusInterval = setInterval(fetchStatus, 2000); // 每2秒更新一次状态
    const logsInterval = setInterval(fetchLogs, 3000); // 每3秒更新一次日志

    return () => {
      clearInterval(statusInterval);
      clearInterval(logsInterval);
    };
  }, [logLevel, logLines]); // 当日志级别或行数变化时重新获取

  // 监听登录状态变化，自动关闭登录对话框
  useEffect(() => {
    if (automationStatus.is_logged_in && showLoginDialog) {
      console.log('检测到登录成功，自动关闭登录对话框');
      setShowLoginDialog(false);
      setSuccess('LinkedIn登录成功');
    }
  }, [automationStatus.is_logged_in, showLoginDialog]);
  
  // 手动刷新日志
  const refreshLogs = () => {
    fetchLogs(true);
  };
  
  // 清空日志
  const clearLogs = () => {
    setLogs([]);
  };

  // 监听用户滚动行为
  const handleLogScroll = (e) => {
    const container = e.target;
    const isAtBottom = container.scrollHeight - container.scrollTop <= container.clientHeight + 10;
    setUserScrolled(!isAtBottom);
  };
  
  // 日志菜单处理函数
// 处理日志级别菜单
  const handleMenuClick = (event) => {
    setAnchorEl(event.currentTarget);
  };
  
  const handleMenuClose = () => {
    setAnchorEl(null);
  };
  
  const handleLogLevelChange = (level) => {
    setLogLevel(level);
    handleMenuClose();
    // 立即刷新日志
    fetchLogs(true);
  };

  // 处理日志行数变更
  const handleLogLinesChange = (lines) => {
    setLogLines(lines);
    handleMenuClose();
    // 立即刷新日志
    fetchLogs(true);
  };

  // 设置自动化
  const setupAutomation = async () => {
    setLoading(true);
    setError('');
    try {
      const response = await fetch(`${API_BASE}/setup`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          headless: loginForm.headless,
          automation_type: loginForm.automation_type
        })
      });
      
      if (response.ok) {
        const result = await response.json();
        if (result.success) {
          setSuccess(result.message);
          fetchStatus();
        } else {
          setError(result.message || '设置自动化失败');
        }
      } else {
        const errorData = await response.json();
        setError(errorData.detail || '设置自动化失败');
      }
    } catch (err) {
      setError('设置自动化失败: ' + err.message);
    }
    setLoading(false);
  };

  // 登录LinkedIn
  const loginLinkedIn = async () => {
    if (!loginForm.email || !loginForm.password) {
      setError('请输入邮箱和密码');
      return;
    }

    // 先设置自动化
    await setupAutomation();

    setLoading(true);
    setError('');
    try {
      const response = await fetch(`${API_BASE}/login`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(loginForm)
      });
      
      const result = await response.json();
      
      if (result.success) {
        setSuccess('LinkedIn登录成功');
        setShowLoginDialog(false);
        fetchStatus();
      } else {
        if (result.requires_action) {
          // 需要用户操作（验证码或二次验证）
          setAutomationStatus(prev => ({
            ...prev,
            current_task: result.status
          }));
          setError('请在浏览器窗口中完成验证，验证完成后重试登录');
        } else {
          setError(result.status || '登录失败');
        }
      }
    } catch (err) {
      setError('登录失败: ' + err.message);
    }
    setLoading(false);
  };

  // 搜索职位
  const searchJobs = async () => {
    if (!automationStatus.is_logged_in) {
      setError('请先登录LinkedIn');
      return;
    }

    setSearchLoading(true);
    setError('');
    try {
      const response = await fetch(`${API_BASE}/search`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(searchForm)
      });

      if (response.ok) {
        const jobs = await response.json();
        updateSearchResults(jobs);
        setSuccess(`找到 ${jobs.length} 个职位`);
      } else {
        const errorData = await response.json();
        setError(errorData.detail || '搜索失败');
      }
    } catch (err) {
      setError('搜索失败: ' + err.message);
    }
    setSearchLoading(false);
  };

  // 申请单个职位
  const applySingleJob = async (job) => {
    setLoading(true);
    setError('');
    try {
      const response = await fetch(`${API_BASE}/apply/${job.job_id}`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(job)
      });
      
      const result = await response.json();
      if (result.success) {
        setSuccess(result.message);
        fetchStatus();
      } else {
        setError(result.message);
      }
    } catch (err) {
      setError('申请失败: ' + err.message);
    }
    setLoading(false);
  };

  // 批量申请职位
  const startBatchApply = async () => {
    if (!automationStatus.is_logged_in) {
      setError('请先登录LinkedIn');
      return;
    }

    setLoading(true);
    setError('');
    try {
      const response = await fetch(`${API_BASE}/batch-apply`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(batchApplyForm)
      });
      
      if (response.ok) {
        setSuccess('批量申请已开始');
        fetchStatus();
      } else {
        const errorData = await response.json();
        setError(errorData.detail || '批量申请失败');
      }
    } catch (err) {
      setError('批量申请失败: ' + err.message);
    }
    setLoading(false);
  };

  // 停止批量申请
  const stopBatchApply = async () => {
    setLoading(true);
    setError('');
    try {
      const response = await fetch(`${API_BASE}/stop`, {
        method: 'POST'
      });
      
      if (response.ok) {
        setSuccess('批量申请已停止');
        fetchStatus();
      } else {
        const errorData = await response.json();
        setError(errorData.detail || '停止批量申请失败');
      }
    } catch (err) {
      setError('停止批量申请失败: ' + err.message);
    }
    setLoading(false);
  };

  // 手动验证登录状态
  const verifyLoginStatus = async () => {
    setLoading(true);
    setError('');
    try {
      const response = await fetch(`${API_BASE}/verify-login`, { method: 'POST' });
      const result = await response.json();
      if (result.success) {
        setSuccess('登录状态验证成功');
        fetchStatus();
      } else {
        setError(result.message || '登录状态验证失败');
      }
    } catch (err) {
      setError('验证失败: ' + err.message);
    }
    setLoading(false);
  };

  // 登出LinkedIn
  const logoutLinkedIn = async () => {
    setLoading(true);
    setError('');
    try {
      const response = await fetch(`${API_BASE}/logout`, { method: 'POST' });
      const result = await response.json();
      if (result.success) {
        setSuccess('已成功登出LinkedIn');
        fetchStatus();
      } else {
        setError(result.message || '登出失败');
      }
    } catch (err) {
      setError('登出失败: ' + err.message);
    }
    setLoading(false);
  };

  // 选择框相关功能
  const handleSelectJob = (jobId, checked) => {
    const newSelected = new Set(selectedJobs);
    if (checked) {
      newSelected.add(jobId);
    } else {
      newSelected.delete(jobId);
    }
    updateSelectedJobs(newSelected);
  };

  const handleSelectAll = (checked) => {
    const newSelected = new Set(selectedJobs);
    if (checked) {
      // 选中当前页面的所有职位
      currentPageJobs.forEach(job => newSelected.add(job.job_id));
    } else {
      // 取消选中当前页面的所有职位
      currentPageJobs.forEach(job => newSelected.delete(job.job_id));
    }
    updateSelectedJobs(newSelected);
  };

  // 分页计算和选择逻辑现在来自全局状态

  // 批量申请选中的职位
  const applySelectedJobs = async () => {
    if (selectedJobs.size === 0) {
      setError('请先选择要申请的职位');
      return;
    }

    setLoading(true);
    setError('');
    let successCount = 0;
    let failCount = 0;

    try {
      for (const jobId of selectedJobs) {
        const job = searchResults.find(j => j.job_id === jobId);
        if (job) {
          try {
            const response = await fetch(`${API_BASE}/apply/${job.job_id}`, {
              method: 'POST',
              headers: { 'Content-Type': 'application/json' },
              body: JSON.stringify(job)
            });

            const result = await response.json();
            if (result.success) {
              successCount++;
            } else {
              failCount++;
            }
          } catch (err) {
            failCount++;
          }
        }
      }

      setSuccess(`批量申请完成：成功 ${successCount} 个，失败 ${failCount} 个`);
      updateSelectedJobs(new Set()); // 清空选择
      fetchStatus();
    } catch (err) {
      setError('批量申请失败: ' + err.message);
    }
    setLoading(false);
  };

  // 日志菜单处理函数已在上方定义

  return (
    <Container maxWidth={false} sx={{
      mt: 4,
      mb: 4,
      maxWidth: '1400px',
      mx: 'auto',
      px: { xs: 2, sm: 3, md: 4 },
      display: 'flex',
      flexDirection: 'column',
      alignItems: 'center',
      width: '100%'
    }}>
      <Box sx={{ mb: 4, alignSelf: 'flex-start' }}>
        <Typography variant="h4" sx={{ mb: 2, color: 'primary.main', fontWeight: 'bold' }}>
          LinkedIn求职
        </Typography>
      </Box>
      
      {/* 日志级别筛选菜单 */}
      <Menu
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={handleMenuClose}
      >
        <MenuItem 
          onClick={() => handleLogLevelChange('ALL')} 
          selected={logLevel === 'ALL'}
        >
          全部
        </MenuItem>
        <MenuItem 
          onClick={() => handleLogLevelChange('DEBUG')} 
          selected={logLevel === 'DEBUG'}
        >
          调试
        </MenuItem>
        <MenuItem 
          onClick={() => handleLogLevelChange('INFO')} 
          selected={logLevel === 'INFO'}
        >
          信息
        </MenuItem>
        <MenuItem 
          onClick={() => handleLogLevelChange('WARNING')} 
          selected={logLevel === 'WARNING'}
        >
          警告
        </MenuItem>
        <MenuItem 
          onClick={() => handleLogLevelChange('ERROR')} 
          selected={logLevel === 'ERROR'}
        >
          错误
        </MenuItem>
        <Divider />
        <MenuItem disabled>
          <Typography variant="body2" color="text.secondary">
            显示行数
          </Typography>
        </MenuItem>
        <MenuItem 
          onClick={() => handleLogLinesChange(20)}
          selected={logLines === 20}
        >
          20 行
        </MenuItem>
        <MenuItem 
          onClick={() => handleLogLinesChange(50)}
          selected={logLines === 50}
        >
          50 行
        </MenuItem>
        <MenuItem 
          onClick={() => handleLogLinesChange(100)}
          selected={logLines === 100}
        >
          100 行
        </MenuItem>
      </Menu>
      
      <Box sx={{
        display: 'flex',
        gap: 3,
        width: '100%',
        justifyContent: 'center',
        alignItems: 'flex-start'
      }}>
        {/* 左侧：运行状态卡片 */}
        <Box sx={{ width: { xs: '100%', md: '25%' }, minWidth: 0, display: 'flex', flexDirection: 'column' }}>
          <Card sx={{ flex: 1, display: 'flex', flexDirection: 'column', minHeight: 0 }}>
            <CardContent sx={{ p: 3, flexGrow: 0 }}>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <InfoIcon sx={{ mr: 1, color: 'primary.main' }} />
                <Typography variant="h6" sx={{ fontWeight: 'bold', color: 'primary.main' }}>
                  运行状态
                </Typography>
              </Box>
              <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                <Typography variant="body1">
                  找到职位: <Box component="span" sx={{ fontWeight: 'bold' }}>{automationStatus.progress.total_found}</Box>
                </Typography>
                <Typography variant="body1">
                  成功申请: <Box component="span" sx={{ fontWeight: 'bold', color: 'success.main' }}>{automationStatus.progress.total_applied}</Box>
                </Typography>
                <Typography variant="body1">
                  申请中职位: <Box component="span" sx={{ fontWeight: 'bold', color: 'info.main' }}>
                    {(() => {
                      const task = automationStatus.current_task;
                      // 过滤掉非申请相关的状态信息
                      if (!task ||
                          task.includes('已设置浏览器') ||
                          task.includes('等待用户操作') ||
                          task.includes('已登录') ||
                          task.includes('已登出') ||
                          task.includes('登录状态') ||
                          task.includes('验证') ||
                          task.includes('设置')) {
                        return '无';
                      }
                      // 提取职位申请信息
                      if (task.includes('正在申请职位：')) {
                        return task.replace(/^.*正在申请职位：/, '').replace(/\s*-\s*.*$/, '');
                      }
                      // 如果包含申请相关关键词，显示原文
                      if (task.includes('申请') || task.includes('投递')) {
                        return task;
                      }
                      return '无';
                    })()}
                  </Box>
                </Typography>
                <Typography variant="body1">
                  自动化类型: <Box component="span" sx={{ fontWeight: 'bold', color: 'primary.main' }}>
                    {loginForm.automation_type === 'playwright_async' ? 'Playwright 异步 (最佳性能)' :
                     loginForm.automation_type === 'playwright' ? 'Playwright 同步 (推荐)' :
                     'Selenium (传统)'}
                  </Box>
                </Typography>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, flexWrap: 'wrap' }}>
                  <Typography variant="body1">
                    登录状态: <Box component="span" sx={{ fontWeight: 'bold', color: automationStatus.is_logged_in ? 'success.main' : 'error.main' }}>{automationStatus.is_logged_in ? '已登录' : '未登录'}</Box>
                  </Typography>
                  {!automationStatus.is_logged_in && (
                    <Button
                      size="small"
                      color="primary"
                      variant="outlined"
                      onClick={verifyLoginStatus}
                      disabled={loading}
                      sx={{ ml: 1, borderRadius: 2 }}
                    >
                      验证登录
                    </Button>
                  )}
                  {automationStatus.is_logged_in && (
                    <Button size="small" color="error" variant="outlined" startIcon={<LogoutIcon />} onClick={logoutLinkedIn} sx={{ ml: 1, borderRadius: 2 }}>
                      退出登录
                    </Button>
                  )}
                </Box>
              </Box>
            </CardContent>
            
            {/* 日志显示器 */}
            <Box sx={{ 
              p: 2, 
              flexGrow: 1, 
              display: 'flex', 
              flexDirection: 'column',
              borderTop: '1px solid',
              borderColor: 'divider'
            }}>
              <Box sx={{ 
                display: 'flex', 
                justifyContent: 'space-between', 
                alignItems: 'center',
                mb: 1
              }}>
                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                  <TerminalIcon sx={{ mr: 1, color: 'text.secondary', fontSize: '1.2rem' }} />
                  <Typography variant="subtitle2" color="text.secondary">
                    运行日志
                  </Typography>
                </Box>
                <Box>
                  <Tooltip title="刷新日志">
                    <IconButton size="small" onClick={refreshLogs}>
                      <RefreshIcon fontSize="small" />
                    </IconButton>
                  </Tooltip>
                  <Tooltip title="筛选级别">
                    <IconButton size="small" onClick={handleMenuClick}>
                      <FilterIcon fontSize="small" />
                    </IconButton>
                  </Tooltip>
                  <Tooltip title="清空日志">
                    <IconButton size="small" onClick={clearLogs}>
                      <ClearIcon fontSize="small" />
                    </IconButton>
                  </Tooltip>
                  <Tooltip title={expandLogs ? "收起日志" : "展开日志"}>
                    <IconButton size="small" onClick={() => setExpandLogs(!expandLogs)}>
                      {expandLogs ? <ExpandLessIcon fontSize="small" /> : <ExpandMoreIcon fontSize="small" />}
                    </IconButton>
                  </Tooltip>
                </Box>
              </Box>
              
              {/* 日志内容 */}
              <Paper
                variant="outlined"
                sx={{
                  flexGrow: 1,
                  height: expandLogs ? '300px' : '150px',
                  overflow: 'auto',
                  bgcolor: 'grey.900',
                  transition: 'height 0.3s ease',
                  p: 1,
                  fontFamily: 'monospace',
                  fontSize: '0.75rem',
                  position: 'relative'
                }}
                ref={logContainerRef}
                onScroll={handleLogScroll}
              >
                {logs.length > 0 ? (
                  <Box component="pre" sx={{ m: 0, color: 'common.white', whiteSpace: 'pre-wrap', wordBreak: 'break-all' }}>
                    {logs.map((log, index) => {
                      // 根据日志级别设置颜色
                      let color = 'inherit';
                      if (log.includes('| ERROR')) color = '#ff5252';
                      if (log.includes('| WARNING')) color = '#ffab40';
                      if (log.includes('| INFO')) color = '#4fc3f7';
                      if (log.includes('| DEBUG')) color = '#b0bec5';
                      
                      return (
                        <Box 
                          key={index} 
                          component="div" 
                          sx={{ 
                            color,
                            lineHeight: 1.5,
                            fontSize: '0.75rem'
                          }}
                        >
                          {log}
                        </Box>
                      );
                    })}
                    <div ref={logEndRef} />
                  </Box>
                ) : (
                  <Box sx={{ color: 'grey.500', display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100%' }}>
                    暂无日志记录
                  </Box>
                )}
              </Paper>
            </Box>
            
            <CardContent sx={{ p: 3, flexGrow: 0, pt: 2 }}>
              <Box>
                <Button
                  variant="contained"
                  color="primary"
                  onClick={() => setShowLoginDialog(true)}
                  startIcon={<SettingsIcon />}
                  fullWidth
                  sx={{ mb: 2, borderRadius: 2 }}
                >
                  登录设置
                </Button>
                <Button
                  variant="contained"
                  color={automationStatus.is_running ? 'error' : 'success'}
                  onClick={automationStatus.is_running ? stopBatchApply : startBatchApply}
                  startIcon={automationStatus.is_running ? <StopIcon /> : <PlayIcon />}
                  disabled={!automationStatus.is_logged_in || loading}
                  fullWidth
                  sx={{ borderRadius: 2 }}
                >
                  {automationStatus.is_running ? '停止批量申请' : '开始批量申请'}
                </Button>
              </Box>
            </CardContent>
          </Card>
        </Box>

        {/* 右侧：其他卡片+搜索结果卡片 */}
        <Box sx={{ width: { xs: '100%', md: '75%' }, minWidth: 0, display: 'flex', flexDirection: 'column', gap: 3 }}>
          {/* 搜索设置 */}
          <Grid item xs={12}>
            <Card sx={{ boxShadow: 3, borderRadius: 2 }}>
              <CardContent sx={{ p: 3 }}>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 3, justifyContent: 'space-between' }}>
                  <Box sx={{ display: 'flex', alignItems: 'center' }}>
                    <SearchIcon sx={{ mr: 1, color: 'primary.main' }} />
                    <Typography variant="h6" sx={{ fontWeight: 'bold', color: 'primary.main' }}>
                      搜索设置
                    </Typography>
                  </Box>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                    <FormControlLabel
                      control={
                        <Switch
                          checked={searchForm.easy_apply_only}
                          onChange={(e) => updateSearchForm({ ...searchForm, easy_apply_only: e.target.checked })}
                          color="primary"
                        />
                      }
                      label="快速申请"
                      sx={{ m: 0 }}
                    />
                    <Button
                      variant="contained"
                      color="primary"
                      onClick={searchJobs}
                      startIcon={searchLoading ? <CircularProgress size={20} color="inherit" /> : <SearchIcon />}
                      disabled={!automationStatus.is_logged_in || searchLoading}
                      sx={{ borderRadius: 2 }}
                    >
                      {searchLoading ? '🤖 AI正在解析职位...' : '搜索职位'}
                    </Button>
                  </Box>
                </Box>
                <Grid container spacing={2}>
                  <Grid item xs={12} md={6}>
                    <TextField
                      label="关键词"
                      value={searchForm.keywords}
                      onChange={(e) => updateSearchForm({ ...searchForm, keywords: e.target.value })}
                      fullWidth
                      variant="outlined"
                      sx={{ '& .MuiOutlinedInput-root': { borderRadius: 2 } }}
                    />
                  </Grid>
                  <Grid item xs={12} md={6}>
                    <TextField
                      label="地点"
                      value={searchForm.location}
                      onChange={(e) => updateSearchForm({ ...searchForm, location: e.target.value })}
                      fullWidth
                      variant="outlined"
                      sx={{ '& .MuiOutlinedInput-root': { borderRadius: 2 } }}
                    />
                  </Grid>
                </Grid>
              </CardContent>
            </Card>
          </Grid>

          {/* 批量申请设置 */}
          <Grid item xs={12}>
            <Card sx={{ boxShadow: 3, borderRadius: 2 }}>
              <CardContent sx={{ p: 3 }}>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
                  <SettingsIcon sx={{ mr: 1, color: 'primary.main' }} />
                  <Typography variant="h6" sx={{ fontWeight: 'bold', color: 'primary.main' }}>
                    批量申请设置
                  </Typography>
                </Box>
                <Grid container spacing={2}>
                  <Grid item xs={12} md={4}>
                    <TextField
                      label="最大申请数量"
                      type="number"
                      value={batchApplyForm.max_applications}
                      onChange={(e) => setBatchApplyForm({ ...batchApplyForm, max_applications: parseInt(e.target.value) })}
                      fullWidth
                      variant="outlined"
                      sx={{ '& .MuiOutlinedInput-root': { borderRadius: 2 } }}
                    />
                  </Grid>
                  <Grid item xs={12} md={4}>
                    <TextField
                      label="关键词（用逗号分隔）"
                      value={batchApplyForm.keywords.join(', ')}
                      onChange={(e) => setBatchApplyForm({ ...batchApplyForm, keywords: e.target.value.split(',').map(k => k.trim()) })}
                      fullWidth
                      variant="outlined"
                      sx={{ '& .MuiOutlinedInput-root': { borderRadius: 2 } }}
                    />
                  </Grid>
                  <Grid item xs={12} md={4}>
                    <TextField
                      label="地点"
                      value={batchApplyForm.location}
                      onChange={(e) => setBatchApplyForm({ ...batchApplyForm, location: e.target.value })}
                      fullWidth
                      variant="outlined"
                      sx={{ '& .MuiOutlinedInput-root': { borderRadius: 2 } }}
                    />
                  </Grid>
                </Grid>
              </CardContent>
            </Card>
          </Grid>
          
          {/* 搜索结果 - 移到批量申请设置下面 */}
          <Grid item xs={12}>
            <Card sx={{ display: 'flex', flexDirection: 'column' }}>
              <CardContent sx={{ display: 'flex', flexDirection: 'column' }}>
                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 2 }}>
                  <Box sx={{ display: 'flex', alignItems: 'center' }}>
                    <SearchIcon sx={{ mr: 1, color: 'primary.main' }} />
                    <Typography variant="h6" sx={{ fontWeight: 'bold', color: 'primary.main' }}>
                      搜索结果
                    </Typography>
                    {searchResults.length > 0 && (
                      <Chip
                        label={`${searchResults.length} 个职位`}
                        size="small"
                        sx={{ ml: 2, bgcolor: 'success.main', color: 'white' }}
                      />
                    )}
                  </Box>
                  {searchResults.length > 0 && (
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <FormControlLabel
                        control={
                          <Checkbox
                            checked={isAllSelected}
                            indeterminate={isIndeterminate}
                            onChange={(e) => handleSelectAll(e.target.checked)}
                            size="small"
                          />
                        }
                        label="全选当前页"
                        sx={{ mr: 1 }}
                      />
                      <Button
                        variant="contained"
                        color="success"
                        size="small"
                        onClick={applySelectedJobs}
                        disabled={loading || selectedJobs.size === 0}
                        sx={{ borderRadius: 2 }}
                      >
                        申请选中 ({selectedJobs.size})
                      </Button>
                    </Box>
                  )}
                </Box>

                {/* 分页控制和每页显示数量选择 */}
                {searchResults.length > 0 && (
                  <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 2, px: 1 }}>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                      <Typography variant="body2" color="text.secondary">
                        每页显示:
                      </Typography>
                      <Select
                        value={itemsPerPage}
                        onChange={(e) => {
                          updateItemsPerPage(e.target.value);
                        }}
                        size="small"
                        sx={{ minWidth: 80 }}
                      >
                        <MenuItem value={10}>10</MenuItem>
                        <MenuItem value={20}>20</MenuItem>
                        <MenuItem value={30}>30</MenuItem>
                        <MenuItem value={50}>50</MenuItem>
                      </Select>
                      <Typography variant="body2" color="text.secondary">
                        显示 {startIndex + 1}-{Math.min(endIndex, searchResults.length)} 共 {searchResults.length} 个
                      </Typography>
                    </Box>

                    {totalPages > 1 && (
                      <Pagination
                        count={totalPages}
                        page={currentPage}
                        onChange={(_, page) => updateCurrentPage(page)}
                        color="primary"
                        size="small"
                        showFirstButton
                        showLastButton
                      />
                    )}
                  </Box>
                )}
                <List sx={{ flexGrow: 1, overflow: 'auto', minHeight: 0, maxHeight: '600px' }}>
                  {currentPageJobs.length > 0 ? (
                    currentPageJobs.map((job, index) => (
                      <React.Fragment key={job.job_id}>
                        <ListItem sx={{ px: 2, py: 1, bgcolor: 'background.paper', borderRadius: 1, mb: 1, alignItems: 'center' }}>
                          <Checkbox
                            checked={selectedJobs.has(job.job_id)}
                            onChange={(e) => handleSelectJob(job.job_id, e.target.checked)}
                            size="small"
                            sx={{ mr: 1 }}
                          />
                          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, flexGrow: 1, minWidth: 0 }}>
                            {/* 职位标题 */}
                            <Typography
                              variant="subtitle2"
                              sx={{
                                fontWeight: 'medium',
                                minWidth: '250px',
                                maxWidth: '250px',
                                overflow: 'hidden',
                                textOverflow: 'ellipsis',
                                whiteSpace: 'nowrap'
                              }}
                            >
                              {job.title}
                            </Typography>

                            {/* 公司名称 */}
                            <Typography
                              variant="body2"
                              color="text.primary"
                              sx={{
                                fontWeight: 'medium',
                                minWidth: '180px',
                                maxWidth: '180px',
                                overflow: 'hidden',
                                textOverflow: 'ellipsis',
                                whiteSpace: 'nowrap'
                              }}
                            >
                              🏢 {job.company}
                            </Typography>

                            {/* 地点 */}
                            <Typography
                              variant="body2"
                              color="text.secondary"
                              sx={{
                                minWidth: '200px',
                                maxWidth: '200px',
                                overflow: 'hidden',
                                textOverflow: 'ellipsis',
                                whiteSpace: 'nowrap'
                              }}
                            >
                              📍 {job.location}
                            </Typography>

                            {/* Easy Apply 标签 */}
                            <Box sx={{ minWidth: '30px', display: 'flex', justifyContent: 'center' }}>
                              {job.is_easy_apply && (
                                <Chip
                                  label="E"
                                  size="small"
                                  color="success"
                                  sx={{ fontSize: '0.7rem', height: '20px', minWidth: '24px' }}
                                />
                              )}
                            </Box>

                            {/* 职位链接 */}
                            <Box sx={{ minWidth: '30px', display: 'flex', justifyContent: 'center' }}>
                              {job.url && (
                                <Link
                                  href={job.url}
                                  target="_blank"
                                  rel="noopener noreferrer"
                                  variant="body2"
                                  sx={{
                                    color: 'primary.main',
                                    textDecoration: 'none',
                                    '&:hover': { textDecoration: 'underline' }
                                  }}
                                >
                                  🔗
                                </Link>
                              )}
                            </Box>
                          </Box>

                          <Button
                            variant="outlined"
                            color="primary"
                            onClick={() => applySingleJob(job)}
                            disabled={loading}
                            size="small"
                            sx={{ borderRadius: 2, minWidth: '80px', ml: 1 }}
                          >
                            申请
                          </Button>
                        </ListItem>
                        {index < currentPageJobs.length - 1 && <Divider sx={{ my: 1 }} />}
                      </React.Fragment>
                    ))
                  ) : (
                    <Typography variant="body2" color="text.secondary" sx={{ p: 2, textAlign: 'left' }}>
                      暂无搜索结果，请点击"搜索职位"按钮开始搜索
                    </Typography>
                  )}
                </List>
              </CardContent>
            </Card>
          </Grid>
        </Box>
      </Box>
      
      {/* 申请记录 - 移到最下面并平铺 */}
      <Grid container spacing={3} sx={{
        mt: 3,
        width: '100%',
        justifyContent: 'center'
      }}>
        <Grid item xs={12}>
          <Card sx={{ boxShadow: 3, borderRadius: 2 }}>
            <CardContent sx={{ p: 3 }}>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <CheckIcon sx={{ mr: 1, color: 'success.main' }} />
                <Typography variant="h6" sx={{ fontWeight: 'bold', color: 'primary.main' }}>
                  申请记录
                </Typography>
              </Box>
              <List sx={{ maxHeight: '400px', overflow: 'auto', pr: 1 }}>
                {automationStatus.progress.successful_applications.length > 0 || automationStatus.progress.failed_applications.length > 0 ? (
                  <>
                    {automationStatus.progress.successful_applications.map((app, index) => (
                      <React.Fragment key={index}>
                        <ListItem sx={{ px: 2, py: 1.5, bgcolor: 'background.paper', borderRadius: 1, mb: 1 }}>
                          <ListItemText
                            primary={<Typography variant="subtitle1" sx={{ fontWeight: 'medium' }}>{app.title}</Typography>}
                            secondary={
                              <>
                                <Typography component="span" variant="body2" color="text.primary" sx={{ fontWeight: 'medium' }}>
                                  {app.company}
                                </Typography>
                                <Typography component="span" variant="body2" color="text.secondary">
                                  {` - ${app.location}`}
                                </Typography>
                              </>
                            }
                          />
                          <Chip
                            label="成功"
                            color="success"
                            size="small"
                            icon={<CheckIcon />}
                            sx={{ borderRadius: 1 }}
                          />
                        </ListItem>
                        {index < automationStatus.progress.successful_applications.length - 1 && <Divider sx={{ my: 1 }} />}
                      </React.Fragment>
                    ))}
                    {automationStatus.progress.failed_applications.map((app, index) => (
                      <React.Fragment key={`failed-${index}`}>
                        <ListItem sx={{ px: 2, py: 1.5, bgcolor: 'background.paper', borderRadius: 1, mb: 1 }}>
                          <ListItemText
                            primary={<Typography variant="subtitle1" sx={{ fontWeight: 'medium' }}>{app.title}</Typography>}
                            secondary={
                              <>
                                <Typography component="span" variant="body2" color="text.primary" sx={{ fontWeight: 'medium' }}>
                                  {app.company}
                                </Typography>
                                <Typography component="span" variant="body2" color="text.secondary">
                                  {` - ${app.location}`}
                                </Typography>
                              </>
                            }
                          />
                          <Chip
                            label="失败"
                            color="error"
                            size="small"
                            icon={<ErrorIcon />}
                            sx={{ borderRadius: 1 }}
                          />
                        </ListItem>
                        {index < automationStatus.progress.failed_applications.length - 1 && <Divider sx={{ my: 1 }} />}
                      </React.Fragment>
                    ))}
                  </>
                ) : (
                  <Typography variant="body2" color="text.secondary" sx={{ p: 2, textAlign: 'left' }}>
                    暂无申请记录，开始批量申请后将在此显示
                  </Typography>
                )}
              </List>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* 登录对话框 */}
      <Dialog
        open={showLoginDialog}
        onClose={() => setShowLoginDialog(false)}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>LinkedIn登录</DialogTitle>
        <DialogContent>
          <Alert severity="info" sx={{ mb: 2 }}>
            登录过程中可能需要进行二次验证，请注意查看浏览器窗口并按提示完成验证。
          </Alert>
          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2, mt: 2 }}>
            <TextField
              label="邮箱"
              value={loginForm.email}
              onChange={(e) => setLoginForm({ ...loginForm, email: e.target.value })}
              fullWidth
            />
            <TextField
              label="密码"
              type="password"
              value={loginForm.password}
              onChange={(e) => setLoginForm({ ...loginForm, password: e.target.value })}
              fullWidth
            />
            <FormControl fullWidth>
              <InputLabel id="automation-type-label">自动化类型</InputLabel>
              <Select
                labelId="automation-type-label"
                value={loginForm.automation_type}
                label="自动化类型"
                onChange={(e) => setLoginForm({ ...loginForm, automation_type: e.target.value })}
              >
                <MenuItem value="playwright_async">Playwright 异步 (最佳性能)</MenuItem>
                <MenuItem value="playwright">Playwright 同步 (推荐)</MenuItem>
                <MenuItem value="selenium">Selenium (传统)</MenuItem>
              </Select>
            </FormControl>
            <FormControlLabel
              control={
                <Switch
                  checked={loginForm.headless}
                  onChange={(e) => setLoginForm({ ...loginForm, headless: e.target.checked })}
                />
              }
              label="无头模式"
            />
            <Typography variant="body2" color="text.secondary">
              注意：建议关闭无头模式，以便处理可能出现的验证码或二次验证。
            </Typography>
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setShowLoginDialog(false)}>取消</Button>
          <Button onClick={loginLinkedIn} variant="contained" disabled={loading}>
            {loading ? '登录中...' : '登录'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* 加载进度条 */}
      {loading && (
        <Box sx={{ width: '100%', position: 'fixed', top: 0, left: 0 }}>
          <LinearProgress />
          {automationStatus.current_task && (
            <Typography variant="body2" sx={{ textAlign: 'center', mt: 1, color: 'primary.main' }}>
              {automationStatus.current_task}
            </Typography>
          )}
        </Box>
      )}

      {/* 错误提示 */}
      <Snackbar
        open={!!error}
        autoHideDuration={6000}
        onClose={() => setError('')}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
        sx={{ mb: 2 }}
      >
        <Alert severity="error" onClose={() => setError('')}>
          {error}
        </Alert>
      </Snackbar>

      {/* 成功提示 */}
      <Snackbar
        open={!!success}
        autoHideDuration={3000}
        onClose={() => setSuccess('')}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
        sx={{ mb: 2 }}
      >
        <Alert severity="success" onClose={() => setSuccess('')}>
          {success}
        </Alert>
      </Snackbar>
    </Container>
  );
};

export default LinkedInAutomation;