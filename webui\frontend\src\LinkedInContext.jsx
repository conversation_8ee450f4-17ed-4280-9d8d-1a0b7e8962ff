import { createContext, useContext, useState, useEffect } from 'react';

// 创建LinkedIn状态上下文
const LinkedInContext = createContext();

// LinkedIn状态提供者组件
export const LinkedInProvider = ({ children }) => {
  // LinkedIn搜索结果状态
  const [searchResults, setSearchResults] = useState([]);
  const [selectedJobs, setSelectedJobs] = useState(new Set());
  const [searchForm, setSearchForm] = useState({
    keywords: 'Python developer',
    location: 'United States',
    easy_apply_only: true
  });
  
  // 分页状态
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);
  
  // 自动化状态
  const [automationStatus, setAutomationStatus] = useState({
    is_running: false,
    is_logged_in: false,
    current_task: null,
    automation_type: 'selenium'
  });
  
  // 批量申请设置
  const [batchSettings, setBatchSettings] = useState({
    max_applications: 10,
    keywords: ['Python developer', 'Software engineer'],
    location: 'United States'
  });

  // 从localStorage恢复状态
  useEffect(() => {
    try {
      const savedSearchResults = localStorage.getItem('linkedin_searchResults');
      const savedSelectedJobs = localStorage.getItem('linkedin_selectedJobs');
      const savedSearchForm = localStorage.getItem('linkedin_searchForm');
      const savedCurrentPage = localStorage.getItem('linkedin_currentPage');
      const savedItemsPerPage = localStorage.getItem('linkedin_itemsPerPage');
      const savedBatchSettings = localStorage.getItem('linkedin_batchSettings');

      if (savedSearchResults) {
        setSearchResults(JSON.parse(savedSearchResults));
      }
      
      if (savedSelectedJobs) {
        setSelectedJobs(new Set(JSON.parse(savedSelectedJobs)));
      }
      
      if (savedSearchForm) {
        setSearchForm(JSON.parse(savedSearchForm));
      }
      
      if (savedCurrentPage) {
        setCurrentPage(parseInt(savedCurrentPage));
      }
      
      if (savedItemsPerPage) {
        setItemsPerPage(parseInt(savedItemsPerPage));
      }
      
      if (savedBatchSettings) {
        setBatchSettings(JSON.parse(savedBatchSettings));
      }
    } catch (error) {
      console.error('恢复LinkedIn状态失败:', error);
    }
  }, []);

  // 保存状态到localStorage
  const saveToLocalStorage = (key, value) => {
    try {
      if (value instanceof Set) {
        localStorage.setItem(key, JSON.stringify([...value]));
      } else {
        localStorage.setItem(key, JSON.stringify(value));
      }
    } catch (error) {
      console.error(`保存${key}到localStorage失败:`, error);
    }
  };

  // 更新搜索结果
  const updateSearchResults = (results) => {
    setSearchResults(results);
    setCurrentPage(1); // 重置到第一页
    setSelectedJobs(new Set()); // 清空选择
    saveToLocalStorage('linkedin_searchResults', results);
    saveToLocalStorage('linkedin_selectedJobs', []);
    saveToLocalStorage('linkedin_currentPage', 1);
  };

  // 更新选中的职位
  const updateSelectedJobs = (newSelectedJobs) => {
    setSelectedJobs(newSelectedJobs);
    saveToLocalStorage('linkedin_selectedJobs', [...newSelectedJobs]);
  };

  // 更新搜索表单
  const updateSearchForm = (newForm) => {
    setSearchForm(newForm);
    saveToLocalStorage('linkedin_searchForm', newForm);
  };

  // 更新当前页
  const updateCurrentPage = (page) => {
    setCurrentPage(page);
    saveToLocalStorage('linkedin_currentPage', page);
  };

  // 更新每页显示数量
  const updateItemsPerPage = (count) => {
    setItemsPerPage(count);
    setCurrentPage(1); // 重置到第一页
    saveToLocalStorage('linkedin_itemsPerPage', count);
    saveToLocalStorage('linkedin_currentPage', 1);
  };

  // 更新批量设置
  const updateBatchSettings = (newSettings) => {
    setBatchSettings(newSettings);
    saveToLocalStorage('linkedin_batchSettings', newSettings);
  };

  // 清除所有LinkedIn数据
  const clearLinkedInData = () => {
    setSearchResults([]);
    setSelectedJobs(new Set());
    setCurrentPage(1);
    setItemsPerPage(10);
    setSearchForm({
      keywords: 'Python developer',
      location: 'United States',
      easy_apply_only: true
    });
    setBatchSettings({
      max_applications: 10,
      keywords: ['Python developer', 'Software engineer'],
      location: 'United States'
    });
    
    // 清除localStorage
    localStorage.removeItem('linkedin_searchResults');
    localStorage.removeItem('linkedin_selectedJobs');
    localStorage.removeItem('linkedin_searchForm');
    localStorage.removeItem('linkedin_currentPage');
    localStorage.removeItem('linkedin_itemsPerPage');
    localStorage.removeItem('linkedin_batchSettings');
  };

  // 分页计算
  const totalPages = Math.ceil(searchResults.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  const currentPageJobs = searchResults.slice(startIndex, endIndex);

  // 选择逻辑（基于当前页面）
  const isAllSelected = currentPageJobs.length > 0 && currentPageJobs.every(job => selectedJobs.has(job.job_id));
  const isIndeterminate = currentPageJobs.some(job => selectedJobs.has(job.job_id)) && !isAllSelected;

  const contextValue = {
    // 状态
    searchResults,
    selectedJobs,
    searchForm,
    currentPage,
    itemsPerPage,
    automationStatus,
    batchSettings,
    
    // 分页计算
    totalPages,
    startIndex,
    endIndex,
    currentPageJobs,
    isAllSelected,
    isIndeterminate,
    
    // 更新函数
    updateSearchResults,
    updateSelectedJobs,
    updateSearchForm,
    updateCurrentPage,
    updateItemsPerPage,
    setAutomationStatus,
    updateBatchSettings,
    clearLinkedInData
  };

  return (
    <LinkedInContext.Provider value={contextValue}>
      {children}
    </LinkedInContext.Provider>
  );
};

// 自定义Hook来使用LinkedIn上下文
export const useLinkedIn = () => {
  const context = useContext(LinkedInContext);
  if (!context) {
    throw new Error('useLinkedIn must be used within a LinkedInProvider');
  }
  return context;
};

export default LinkedInContext;
